package main

func main() {
	balancer := NewBalancer()
	// 初始化令牌
	_ = balancer.AddNewToken(1000)
	_ = balancer.AddNewToken(500)
	// 初始化接收器
	_ = balancer.AddNewReceiver("token-1")
	_ = balancer.AddNewReceiver("token-2")
	// 分配新产品
	receiverID, tokenID := balancer.AllocateProduct()
	println("分配接收器:", receiverID, "令牌:", tokenID)
	receiverID, tokenID = balancer.AllocateProduct()
	println("分配接收器:", receiverID, "令牌:", tokenID)
}
