package main

import (
	"math"
	"strconv"
	"sync"
)

var (
	receiverCounter int
	tokenCounter    int
	mutex           sync.Mutex // 保证并发安全
)

type Token struct {
	ID      string
	Limit   int
	Used    int
	ConnNum int
}

type Receiver struct {
	ID      string
	TokenID string
	Used    int
}

type Balancer struct {
	Receivers map[string]*Receiver
	Tokens    map[string]*Token
}

func NewBalancer() *Balancer {
	return &Balancer{
		Receivers: make(map[string]*Receiver),
		Tokens:    make(map[string]*Token),
	}
}

func (b *Balancer) AddNewToken(limit int) *Token {
	tokenID := generateTokenID()
	token := &Token{
		ID:    tokenID,
		Limit: limit,
		Used:  0,
	}
	b.Tokens[tokenID] = token
	return token
}

func (b *Balancer) AddNewReceiver(tokenID string) *Receiver {
	receiverID := generateReceiverID()
	receiver := &Receiver{
		ID:      receiverID,
		TokenID: tokenID,
		Used:    0,
	}
	b.Receivers[receiverID] = receiver
	return receiver
}

func (b *Balancer) AllocateProduct() (receiverID, tokenID string) {
	mutex.Lock()
	defer mutex.Unlock()

	// 尝试使用现有接收器（优先选择负载最轻的）
	bestScore := math.MaxFloat64
	var bestReceiver *Receiver
	for _, r := range b.Receivers {
		token, exists := b.Tokens[r.TokenID]
		if !exists {
			continue
		}
		if r.Used+1 <= 500 && token.Used+1 <= token.Limit {
			// 评估分数：选择（接收器使用率 + token使用率）最低的组合
			receiverUtil := float64(r.Used) / 500
			tokenUtil := float64(token.Used) / float64(token.Limit)
			score := receiverUtil + tokenUtil
			if score < bestScore {
				bestScore = score
				bestReceiver = r
			}
		}
	}
	if bestReceiver != nil {
		token := b.Tokens[bestReceiver.TokenID]
		bestReceiver.Used++
		token.Used++
		return bestReceiver.ID, bestReceiver.TokenID
	}

	// 找不到合适的现有接收器，创建新接收器并分配令牌
	// 优先选择有容量的现有令牌
	bestTokenScore := math.MaxFloat64
	var bestToken *Token
	for _, t := range b.Tokens {
		if t.Used+1 <= t.Limit && t.ConnNum < 10 {
			util := float64(t.Used) / float64(t.Limit)
			if util < bestTokenScore {
				bestTokenScore = util
				bestToken = t
			}
		}
	}
	if bestToken == nil {
		// 没有合适的令牌，创建新令牌（默认限额500）
		bestToken = b.AddNewToken(500)
	}

	// 创建新接收器并关联令牌
	newReceiver := b.AddNewReceiver(bestToken.ID)
	newReceiver.Used = 1
	bestToken.Used++
	bestToken.ConnNum++

	return newReceiver.ID, bestToken.ID
}

func generateReceiverID() string {
	receiverCounter++
	return "receiver-" + strconv.Itoa(receiverCounter)
}

func generateTokenID() string {
	tokenCounter++
	return "token-" + strconv.Itoa(tokenCounter)
}
