package main

import (
	"encoding/json"
	"os"
	"time"
)

// 生产环境配置
type Config struct {
	// 负载均衡配置
	LoadBalancer LoadBalancerConfig `json:"load_balancer"`
	
	// 性能配置
	Performance PerformanceConfig `json:"performance"`
	
	// 监控配置
	Monitoring MonitoringConfig `json:"monitoring"`
	
	// 告警配置
	Alerting AlertingConfig `json:"alerting"`
}

type LoadBalancerConfig struct {
	// 默认令牌限制
	DefaultTokenLimit int `json:"default_token_limit"`
	
	// 默认接收器限制
	DefaultReceiverLimit int `json:"default_receiver_limit"`
	
	// 每个令牌最大连接数
	MaxConnectionsPerToken int `json:"max_connections_per_token"`
	
	// 堆重建阈值
	HeapRebuildThreshold int `json:"heap_rebuild_threshold"`
	
	// 是否启用自动清理
	EnableAutoCleanup bool `json:"enable_auto_cleanup"`
	
	// 清理间隔
	CleanupInterval time.Duration `json:"cleanup_interval"`
}

type PerformanceConfig struct {
	// 是否启用优化版本
	UseOptimizedVersion bool `json:"use_optimized_version"`
	
	// 预分配容量
	PreallocatedReceivers int `json:"preallocated_receivers"`
	PreallocatedTokens    int `json:"preallocated_tokens"`
	
	// GC 调优
	GCTargetPercentage int `json:"gc_target_percentage"`
	
	// 并发控制
	MaxConcurrentAllocations int `json:"max_concurrent_allocations"`
	
	// 批量操作大小
	BatchSize int `json:"batch_size"`
}

type MonitoringConfig struct {
	// 是否启用监控
	Enabled bool `json:"enabled"`
	
	// 指标收集间隔
	MetricsInterval time.Duration `json:"metrics_interval"`
	
	// 指标保留时间
	MetricsRetention time.Duration `json:"metrics_retention"`
	
	// 是否启用详细日志
	VerboseLogging bool `json:"verbose_logging"`
	
	// 导出格式 (prometheus, json, etc.)
	ExportFormat string `json:"export_format"`
	
	// 导出端点
	ExportEndpoint string `json:"export_endpoint"`
}

type AlertingConfig struct {
	// 是否启用告警
	Enabled bool `json:"enabled"`
	
	// 性能阈值
	MaxAllocationTime time.Duration `json:"max_allocation_time"`
	MinQPS            int64         `json:"min_qps"`
	MaxLockContention int64         `json:"max_lock_contention"`
	
	// 告警通道
	WebhookURL    string `json:"webhook_url"`
	EmailRecipients []string `json:"email_recipients"`
	
	// 告警频率限制
	AlertCooldown time.Duration `json:"alert_cooldown"`
}

// 默认配置
func DefaultConfig() *Config {
	return &Config{
		LoadBalancer: LoadBalancerConfig{
			DefaultTokenLimit:      1000,
			DefaultReceiverLimit:   500,
			MaxConnectionsPerToken: 10,
			HeapRebuildThreshold:   1000,
			EnableAutoCleanup:      true,
			CleanupInterval:        5 * time.Minute,
		},
		Performance: PerformanceConfig{
			UseOptimizedVersion:      true,
			PreallocatedReceivers:    1000,
			PreallocatedTokens:       100,
			GCTargetPercentage:       100,
			MaxConcurrentAllocations: 1000,
			BatchSize:                100,
		},
		Monitoring: MonitoringConfig{
			Enabled:          true,
			MetricsInterval:  10 * time.Second,
			MetricsRetention: 24 * time.Hour,
			VerboseLogging:   false,
			ExportFormat:     "prometheus",
			ExportEndpoint:   ":8080/metrics",
		},
		Alerting: AlertingConfig{
			Enabled:           true,
			MaxAllocationTime: 10 * time.Millisecond,
			MinQPS:            1000,
			MaxLockContention: 1000,
			AlertCooldown:     5 * time.Minute,
		},
	}
}

// 从文件加载配置
func LoadConfig(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	
	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, err
	}
	
	return &config, nil
}

// 保存配置到文件
func (c *Config) SaveToFile(filename string) error {
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return err
	}
	
	return os.WriteFile(filename, data, 0644)
}

// 验证配置
func (c *Config) Validate() []string {
	var errors []string
	
	if c.LoadBalancer.DefaultTokenLimit <= 0 {
		errors = append(errors, "default_token_limit must be positive")
	}
	
	if c.LoadBalancer.DefaultReceiverLimit <= 0 {
		errors = append(errors, "default_receiver_limit must be positive")
	}
	
	if c.LoadBalancer.MaxConnectionsPerToken <= 0 {
		errors = append(errors, "max_connections_per_token must be positive")
	}
	
	if c.Performance.MaxConcurrentAllocations <= 0 {
		errors = append(errors, "max_concurrent_allocations must be positive")
	}
	
	if c.Alerting.MaxAllocationTime <= 0 {
		errors = append(errors, "max_allocation_time must be positive")
	}
	
	return errors
}

// 应用配置到负载均衡器
func (c *Config) ApplyToBalancer(balancer *MonitoredBalancer) {
	// 设置告警阈值
	balancer.maxAllocTimeThreshold = c.Alerting.MaxAllocationTime
	balancer.qpsThreshold = c.Alerting.MinQPS
	
	// 设置告警回调
	if c.Alerting.Enabled {
		balancer.SetAlertCallbacks(
			func(duration time.Duration) {
				// 发送慢分配告警
				sendAlert("slow_allocation", map[string]interface{}{
					"duration": duration.String(),
					"threshold": c.Alerting.MaxAllocationTime.String(),
				})
			},
			func(contentions int64) {
				// 发送锁竞争告警
				sendAlert("high_lock_contention", map[string]interface{}{
					"contentions": contentions,
					"threshold": c.Alerting.MaxLockContention,
				})
			},
			func(qps int64) {
				// 发送低 QPS 告警
				sendAlert("low_qps", map[string]interface{}{
					"current_qps": qps,
					"threshold": c.Alerting.MinQPS,
				})
			},
		)
	}
}

// 发送告警 (简化版本)
func sendAlert(alertType string, data map[string]interface{}) {
	// 这里应该实现实际的告警发送逻辑
	// 例如：发送到 webhook、邮件、Slack 等
	println("ALERT:", alertType, "data:", data)
}
