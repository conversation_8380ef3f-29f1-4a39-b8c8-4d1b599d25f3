# 生产环境部署和运维指南

## 性能问题总结

### 原版本主要问题
1. **全局锁竞争** - 所有操作串行化，高并发性能差
2. **O(n) 线性搜索** - 随数据量增长性能线性下降
3. **重复计算** - 每次分配都重新计算分数
4. **内存分配压力** - 频繁创建对象导致 GC 压力

### 性能测试结果
| 场景规模 | 原版本 QPS | 优化版本 QPS | 性能提升 |
|---------|-----------|-------------|---------|
| 小规模   | 771K      | 1.07M       | 1.39x   |
| 中等规模 | 55K       | 87K         | 1.58x   |
| 大规模   | 4K        | 9.5K        | 2.33x   |

## 部署建议

### 1. 硬件配置
```
生产环境推荐配置：
- CPU: 8核以上
- 内存: 16GB以上
- 网络: 千兆网卡
- 存储: SSD（用于日志和配置）
```

### 2. 系统调优
```bash
# 调整文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 调整网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf

# 应用配置
sysctl -p
```

### 3. Go 运行时调优
```bash
# 设置 GOMAXPROCS
export GOMAXPROCS=8

# 调整 GC 目标
export GOGC=100

# 启用 CPU 性能分析
export GODEBUG=gctrace=1
```

## 监控指标

### 关键性能指标
1. **QPS** - 每秒查询数
2. **平均响应时间** - 分配操作耗时
3. **P99 响应时间** - 99% 请求的响应时间
4. **错误率** - 分配失败比例
5. **锁竞争次数** - 锁等待情况

### 资源使用指标
1. **CPU 使用率** - 应保持在 70% 以下
2. **内存使用率** - 监控 GC 频率和内存泄漏
3. **网络 I/O** - 监控网络延迟和吞吐量
4. **活跃连接数** - 监控并发连接数

### 业务指标
1. **活跃 Token 数量**
2. **活跃 Receiver 数量**
3. **Token 使用率分布**
4. **Receiver 负载分布**

## 告警配置

### 性能告警
```json
{
  "alerts": [
    {
      "name": "high_response_time",
      "condition": "avg_allocation_time > 50ms",
      "severity": "warning"
    },
    {
      "name": "low_qps",
      "condition": "current_qps < 5000",
      "severity": "critical"
    },
    {
      "name": "high_error_rate",
      "condition": "error_rate > 1%",
      "severity": "critical"
    }
  ]
}
```

### 资源告警
```json
{
  "alerts": [
    {
      "name": "high_cpu",
      "condition": "cpu_usage > 80%",
      "severity": "warning"
    },
    {
      "name": "high_memory",
      "condition": "memory_usage > 85%",
      "severity": "warning"
    },
    {
      "name": "high_gc_frequency",
      "condition": "gc_frequency > 10/min",
      "severity": "warning"
    }
  ]
}
```

## 容量规划

### 性能基准
基于测试结果，单实例性能预估：
- **小规模** (< 1K Token): 100K+ QPS
- **中等规模** (1K-10K Token): 50K-100K QPS  
- **大规模** (> 10K Token): 10K-50K QPS

### 扩展策略
1. **垂直扩展**: 增加 CPU 和内存
2. **水平扩展**: 部署多个实例 + 负载均衡
3. **分片策略**: 按业务维度分片

## 故障排查

### 常见问题
1. **响应时间过长**
   - 检查锁竞争情况
   - 检查数据量是否过大
   - 检查 GC 频率

2. **QPS 下降**
   - 检查 CPU 使用率
   - 检查网络延迟
   - 检查是否有死锁

3. **内存泄漏**
   - 检查 Token/Receiver 清理
   - 检查 goroutine 泄漏
   - 使用 pprof 分析

### 调试工具
```bash
# 性能分析
go tool pprof http://localhost:8080/debug/pprof/profile

# 内存分析
go tool pprof http://localhost:8080/debug/pprof/heap

# goroutine 分析
go tool pprof http://localhost:8080/debug/pprof/goroutine

# 实时监控
curl http://localhost:8080/metrics
curl http://localhost:8080/health
```

## 最佳实践

### 1. 配置管理
- 使用配置文件而非硬编码
- 支持热重载配置
- 环境隔离（开发/测试/生产）

### 2. 日志管理
- 结构化日志（JSON 格式）
- 合理的日志级别
- 日志轮转和归档

### 3. 安全考虑
- API 认证和授权
- 限流和防护
- 敏感信息脱敏

### 4. 高可用
- 多实例部署
- 健康检查
- 优雅关闭
- 故障转移

### 5. 持续优化
- 定期性能测试
- 监控指标分析
- 代码性能分析
- 容量规划更新

## 升级策略

### 版本升级
1. **灰度发布**: 先在小部分流量上验证
2. **蓝绿部署**: 保持旧版本作为回退
3. **滚动更新**: 逐步替换实例

### 配置变更
1. **向后兼容**: 新配置项有默认值
2. **验证机制**: 配置变更前验证
3. **回滚机制**: 支持快速回滚配置

这个指南涵盖了从性能问题分析到生产环境部署的完整流程，可以帮助您在实际环境中稳定运行负载均衡器。
