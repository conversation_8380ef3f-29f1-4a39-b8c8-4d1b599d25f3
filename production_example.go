package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"runtime"
	"sync"
	"time"
)

// 生产环境负载均衡器服务
type ProductionBalancerService struct {
	balancer *MonitoredBalancer
	config   *Config
	
	// 优雅关闭
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	
	// HTTP 服务器
	httpServer *http.Server
}

func NewProductionBalancerService(configFile string) (*ProductionBalancerService, error) {
	// 加载配置
	config, err := LoadConfig(configFile)
	if err != nil {
		// 使用默认配置
		config = DefaultConfig()
		log.Printf("使用默认配置: %v", err)
	}
	
	// 验证配置
	if errors := config.Validate(); len(errors) > 0 {
		return nil, fmt.Errorf("配置验证失败: %v", errors)
	}
	
	// 创建监控负载均衡器
	balancer := NewMonitoredBalancer()
	
	// 应用配置
	config.ApplyToBalancer(balancer)
	
	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	
	// 创建服务
	service := &ProductionBalancerService{
		balancer: balancer,
		config:   config,
		ctx:      ctx,
		cancel:   cancel,
	}
	
	// 设置 HTTP 服务器
	service.setupHTTPServer()
	
	return service, nil
}

func (s *ProductionBalancerService) setupHTTPServer() {
	mux := http.NewServeMux()
	
	// 分配接口
	mux.HandleFunc("/allocate", s.handleAllocate)
	
	// 健康检查接口
	mux.HandleFunc("/health", s.handleHealth)
	
	// 指标接口
	mux.HandleFunc("/metrics", s.handleMetrics)
	
	// 配置接口
	mux.HandleFunc("/config", s.handleConfig)
	
	// 管理接口
	mux.HandleFunc("/admin/reset", s.handleReset)
	mux.HandleFunc("/admin/gc", s.handleGC)
	
	s.httpServer = &http.Server{
		Addr:    ":8080",
		Handler: mux,
		
		// 生产环境超时设置
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
}

// 分配处理器
func (s *ProductionBalancerService) handleAllocate(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	
	// 执行分配
	receiverID, tokenID := s.balancer.AllocateProductWithMonitoring()
	
	// 返回结果
	w.Header().Set("Content-Type", "application/json")
	fmt.Fprintf(w, `{"receiver_id": "%s", "token_id": "%s"}`, receiverID, tokenID)
}

// 健康检查处理器
func (s *ProductionBalancerService) handleHealth(w http.ResponseWriter, r *http.Request) {
	health := s.balancer.HealthCheck()
	
	w.Header().Set("Content-Type", "application/json")
	
	// 根据健康状态设置 HTTP 状态码
	status := health["status"].(string)
	if status != "healthy" {
		w.WriteHeader(http.StatusServiceUnavailable)
	}
	
	// 返回健康检查结果
	fmt.Fprintf(w, `{
		"status": "%s",
		"issues": %v,
		"timestamp": "%s"
	}`, status, health["issues"], time.Now().Format(time.RFC3339))
}

// 指标处理器
func (s *ProductionBalancerService) handleMetrics(w http.ResponseWriter, r *http.Request) {
	metrics := s.balancer.GetMetrics()
	
	w.Header().Set("Content-Type", "text/plain")
	
	// Prometheus 格式输出
	fmt.Fprintf(w, `# HELP balancer_total_allocations Total number of allocations
# TYPE balancer_total_allocations counter
balancer_total_allocations %d

# HELP balancer_success_allocations Number of successful allocations
# TYPE balancer_success_allocations counter
balancer_success_allocations %d

# HELP balancer_avg_allocation_time_ns Average allocation time in nanoseconds
# TYPE balancer_avg_allocation_time_ns gauge
balancer_avg_allocation_time_ns %d

# HELP balancer_max_allocation_time_ns Maximum allocation time in nanoseconds
# TYPE balancer_max_allocation_time_ns gauge
balancer_max_allocation_time_ns %d

# HELP balancer_current_qps Current queries per second
# TYPE balancer_current_qps gauge
balancer_current_qps %d

# HELP balancer_active_receivers Number of active receivers
# TYPE balancer_active_receivers gauge
balancer_active_receivers %d

# HELP balancer_active_tokens Number of active tokens
# TYPE balancer_active_tokens gauge
balancer_active_tokens %d
`,
		metrics.TotalAllocations,
		metrics.SuccessAllocations,
		metrics.AvgAllocTime,
		metrics.MaxAllocTime,
		metrics.CurrentQPS,
		metrics.ActiveReceivers,
		metrics.ActiveTokens,
	)
}

// 配置处理器
func (s *ProductionBalancerService) handleConfig(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	// 返回当前配置（脱敏）
	fmt.Fprintf(w, `{
		"load_balancer": {
			"default_token_limit": %d,
			"default_receiver_limit": %d,
			"max_connections_per_token": %d
		},
		"performance": {
			"use_optimized_version": %t,
			"max_concurrent_allocations": %d
		},
		"monitoring": {
			"enabled": %t,
			"metrics_interval": "%s"
		}
	}`,
		s.config.LoadBalancer.DefaultTokenLimit,
		s.config.LoadBalancer.DefaultReceiverLimit,
		s.config.LoadBalancer.MaxConnectionsPerToken,
		s.config.Performance.UseOptimizedVersion,
		s.config.Performance.MaxConcurrentAllocations,
		s.config.Monitoring.Enabled,
		s.config.Monitoring.MetricsInterval,
	)
}

// 重置处理器
func (s *ProductionBalancerService) handleReset(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	
	s.balancer.ResetMetrics()
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"message": "Metrics reset successfully"}`)
}

// GC 处理器
func (s *ProductionBalancerService) handleGC(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	
	runtime.GC()
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"message": "GC triggered successfully"}`)
}

// 启动服务
func (s *ProductionBalancerService) Start() error {
	log.Printf("启动生产环境负载均衡器服务...")
	
	// 启动监控协程
	if s.config.Monitoring.Enabled {
		s.wg.Add(1)
		go s.monitoringLoop()
	}
	
	// 启动清理协程
	if s.config.LoadBalancer.EnableAutoCleanup {
		s.wg.Add(1)
		go s.cleanupLoop()
	}
	
	// 启动 HTTP 服务器
	log.Printf("HTTP 服务器启动在 %s", s.httpServer.Addr)
	return s.httpServer.ListenAndServe()
}

// 优雅关闭
func (s *ProductionBalancerService) Shutdown() error {
	log.Printf("正在关闭服务...")
	
	// 取消上下文
	s.cancel()
	
	// 关闭 HTTP 服务器
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := s.httpServer.Shutdown(ctx); err != nil {
		return err
	}
	
	// 等待所有协程结束
	s.wg.Wait()
	
	log.Printf("服务已关闭")
	return nil
}

// 监控循环
func (s *ProductionBalancerService) monitoringLoop() {
	defer s.wg.Done()
	
	ticker := time.NewTicker(s.config.Monitoring.MetricsInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			// 收集和记录指标
			metrics := s.balancer.GetMetrics()
			if s.config.Monitoring.VerboseLogging {
				log.Printf("指标: QPS=%d, 平均时间=%dns, 总分配=%d",
					metrics.CurrentQPS,
					metrics.AvgAllocTime,
					metrics.TotalAllocations,
				)
			}
		}
	}
}

// 清理循环
func (s *ProductionBalancerService) cleanupLoop() {
	defer s.wg.Done()
	
	ticker := time.NewTicker(s.config.LoadBalancer.CleanupInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			// 执行清理操作
			runtime.GC()
			if s.config.Monitoring.VerboseLogging {
				log.Printf("执行定期清理")
			}
		}
	}
}
