package main

import (
	"sync/atomic"
	"time"
)

// 性能监控指标
type Metrics struct {
	// 分配相关指标
	TotalAllocations    int64 // 总分配次数
	SuccessAllocations  int64 // 成功分配次数
	FailedAllocations   int64 // 失败分配次数
	
	// 性能指标
	AvgAllocTime        int64 // 平均分配时间 (纳秒)
	MaxAllocTime        int64 // 最大分配时间 (纳秒)
	CurrentQPS          int64 // 当前 QPS
	
	// 资源使用指标
	ActiveReceivers     int64 // 活跃接收器数量
	ActiveTokens        int64 // 活跃令牌数量
	TotalTokenUsage     int64 // 总令牌使用量
	
	// 锁竞争指标
	LockWaitTime        int64 // 锁等待时间
	LockContentions     int64 // 锁竞争次数
	
	// 时间窗口统计
	LastMinuteAllocs    int64 // 最近一分钟分配次数
	LastHourAllocs      int64 // 最近一小时分配次数
}

// 带监控的负载均衡器
type MonitoredBalancer struct {
	*BalancerOpt
	metrics *Metrics
	
	// 性能阈值配置
	maxAllocTimeThreshold time.Duration // 分配时间阈值
	qpsThreshold          int64         // QPS 阈值
	
	// 告警回调
	onSlowAllocation func(duration time.Duration)
	onHighContention func(contentions int64)
	onLowQPS         func(qps int64)
}

func NewMonitoredBalancer() *MonitoredBalancer {
	return &MonitoredBalancer{
		BalancerOpt:           NewBalancerOpt(),
		metrics:               &Metrics{},
		maxAllocTimeThreshold: 10 * time.Millisecond, // 10ms 阈值
		qpsThreshold:          1000,                   // 1000 QPS 阈值
	}
}

// 带监控的分配方法
func (mb *MonitoredBalancer) AllocateProductWithMonitoring() (receiverID, tokenID string) {
	start := time.Now()
	
	// 记录分配开始
	atomic.AddInt64(&mb.metrics.TotalAllocations, 1)
	
	// 执行分配
	receiverID, tokenID = mb.BalancerOpt.AllocateProduct()
	
	// 记录分配完成
	duration := time.Since(start)
	durationNanos := duration.Nanoseconds()
	
	// 更新性能指标
	atomic.AddInt64(&mb.metrics.SuccessAllocations, 1)
	
	// 更新平均时间 (简化版本，实际应该使用滑动窗口)
	currentAvg := atomic.LoadInt64(&mb.metrics.AvgAllocTime)
	newAvg := (currentAvg + durationNanos) / 2
	atomic.StoreInt64(&mb.metrics.AvgAllocTime, newAvg)
	
	// 更新最大时间
	for {
		currentMax := atomic.LoadInt64(&mb.metrics.MaxAllocTime)
		if durationNanos <= currentMax {
			break
		}
		if atomic.CompareAndSwapInt64(&mb.metrics.MaxAllocTime, currentMax, durationNanos) {
			break
		}
	}
	
	// 检查性能阈值
	if duration > mb.maxAllocTimeThreshold && mb.onSlowAllocation != nil {
		mb.onSlowAllocation(duration)
	}
	
	return receiverID, tokenID
}

// 获取性能指标
func (mb *MonitoredBalancer) GetMetrics() Metrics {
	return Metrics{
		TotalAllocations:   atomic.LoadInt64(&mb.metrics.TotalAllocations),
		SuccessAllocations: atomic.LoadInt64(&mb.metrics.SuccessAllocations),
		FailedAllocations:  atomic.LoadInt64(&mb.metrics.FailedAllocations),
		AvgAllocTime:       atomic.LoadInt64(&mb.metrics.AvgAllocTime),
		MaxAllocTime:       atomic.LoadInt64(&mb.metrics.MaxAllocTime),
		CurrentQPS:         atomic.LoadInt64(&mb.metrics.CurrentQPS),
		ActiveReceivers:    atomic.LoadInt64(&mb.metrics.ActiveReceivers),
		ActiveTokens:       atomic.LoadInt64(&mb.metrics.ActiveTokens),
		TotalTokenUsage:    atomic.LoadInt64(&mb.metrics.TotalTokenUsage),
		LockWaitTime:       atomic.LoadInt64(&mb.metrics.LockWaitTime),
		LockContentions:    atomic.LoadInt64(&mb.metrics.LockContentions),
		LastMinuteAllocs:   atomic.LoadInt64(&mb.metrics.LastMinuteAllocs),
		LastHourAllocs:     atomic.LoadInt64(&mb.metrics.LastHourAllocs),
	}
}

// 设置告警回调
func (mb *MonitoredBalancer) SetAlertCallbacks(
	onSlow func(time.Duration),
	onContention func(int64),
	onLowQPS func(int64),
) {
	mb.onSlowAllocation = onSlow
	mb.onHighContention = onContention
	mb.onLowQPS = onLowQPS
}

// 重置指标
func (mb *MonitoredBalancer) ResetMetrics() {
	atomic.StoreInt64(&mb.metrics.TotalAllocations, 0)
	atomic.StoreInt64(&mb.metrics.SuccessAllocations, 0)
	atomic.StoreInt64(&mb.metrics.FailedAllocations, 0)
	atomic.StoreInt64(&mb.metrics.AvgAllocTime, 0)
	atomic.StoreInt64(&mb.metrics.MaxAllocTime, 0)
	atomic.StoreInt64(&mb.metrics.CurrentQPS, 0)
	atomic.StoreInt64(&mb.metrics.LockWaitTime, 0)
	atomic.StoreInt64(&mb.metrics.LockContentions, 0)
	atomic.StoreInt64(&mb.metrics.LastMinuteAllocs, 0)
	atomic.StoreInt64(&mb.metrics.LastHourAllocs, 0)
}

// 健康检查
func (mb *MonitoredBalancer) HealthCheck() map[string]interface{} {
	metrics := mb.GetMetrics()
	
	status := "healthy"
	issues := []string{}
	
	// 检查平均分配时间
	avgTime := time.Duration(metrics.AvgAllocTime)
	if avgTime > mb.maxAllocTimeThreshold {
		status = "degraded"
		issues = append(issues, "high_allocation_time")
	}
	
	// 检查 QPS
	if metrics.CurrentQPS < mb.qpsThreshold {
		status = "degraded"
		issues = append(issues, "low_qps")
	}
	
	// 检查锁竞争
	if metrics.LockContentions > 1000 {
		status = "degraded"
		issues = append(issues, "high_lock_contention")
	}
	
	return map[string]interface{}{
		"status":  status,
		"issues":  issues,
		"metrics": metrics,
		"uptime":  time.Since(time.Now()), // 简化版本
	}
}
