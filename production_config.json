{"load_balancer": {"default_token_limit": 2000, "default_receiver_limit": 1000, "max_connections_per_token": 20, "heap_rebuild_threshold": 5000, "enable_auto_cleanup": true, "cleanup_interval": "300s"}, "performance": {"use_optimized_version": true, "preallocated_receivers": 10000, "preallocated_tokens": 1000, "gc_target_percentage": 100, "max_concurrent_allocations": 5000, "batch_size": 500}, "monitoring": {"enabled": true, "metrics_interval": "30s", "metrics_retention": "24h", "verbose_logging": false, "export_format": "prometheus", "export_endpoint": ":8080/metrics"}, "alerting": {"enabled": true, "max_allocation_time": "50ms", "min_qps": 5000, "max_lock_contention": 10000, "webhook_url": "https://your-webhook-url.com/alerts", "email_recipients": ["<EMAIL>", "<EMAIL>"], "alert_cooldown": "300s"}}