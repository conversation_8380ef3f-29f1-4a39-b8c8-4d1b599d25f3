package main

import (
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"
)

// 性能测试：对比原版本和优化版本

func BenchmarkOriginalBalancer(b *testing.B) {
	balancer := NewBalancer()
	
	// 预先添加一些 tokens 和 receivers
	for i := 0; i < 100; i++ {
		balancer.AddNewToken(1000)
	}
	for i := 0; i < 500; i++ {
		tokenID := fmt.Sprintf("token-%d", (i%100)+1)
		balancer.AddNewReceiver(tokenID)
	}
	
	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			balancer.AllocateProduct()
		}
	})
}

func BenchmarkOptimizedBalancer(b *testing.B) {
	balancer := NewBalancerOpt()
	
	// 预先添加一些 tokens 和 receivers
	for i := 0; i < 100; i++ {
		balancer.AddNewToken(1000)
	}
	for i := 0; i < 500; i++ {
		tokenID := fmt.Sprintf("token-%d", (i%100)+1)
		balancer.AddNewReceiver(tokenID)
	}
	
	b.<PERSON><PERSON><PERSON>ime<PERSON>()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			balancer.AllocateProduct()
		}
	})
}

// 并发测试
func TestConcurrentAllocations(t *testing.T) {
	fmt.Println("=== 并发性能测试 ===")
	
	// 测试原版本
	fmt.Println("测试原版本...")
	testConcurrentOriginal()
	
	// 测试优化版本
	fmt.Println("测试优化版本...")
	testConcurrentOptimized()
}

func testConcurrentOriginal() {
	balancer := NewBalancer()
	
	// 预先添加数据
	for i := 0; i < 50; i++ {
		balancer.AddNewToken(1000)
	}
	for i := 0; i < 200; i++ {
		tokenID := fmt.Sprintf("token-%d", (i%50)+1)
		balancer.AddNewReceiver(tokenID)
	}
	
	const numGoroutines = 100
	const allocationsPerGoroutine = 1000
	
	var wg sync.WaitGroup
	start := time.Now()
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < allocationsPerGoroutine; j++ {
				balancer.AllocateProduct()
			}
		}()
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	totalAllocations := numGoroutines * allocationsPerGoroutine
	fmt.Printf("原版本: %d 次分配，耗时: %v，QPS: %.2f\n", 
		totalAllocations, duration, float64(totalAllocations)/duration.Seconds())
}

func testConcurrentOptimized() {
	balancer := NewBalancerOpt()
	
	// 预先添加数据
	for i := 0; i < 50; i++ {
		balancer.AddNewToken(1000)
	}
	for i := 0; i < 200; i++ {
		tokenID := fmt.Sprintf("token-%d", (i%50)+1)
		balancer.AddNewReceiver(tokenID)
	}
	
	const numGoroutines = 100
	const allocationsPerGoroutine = 1000
	
	var wg sync.WaitGroup
	start := time.Now()
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < allocationsPerGoroutine; j++ {
				balancer.AllocateProduct()
			}
		}()
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	totalAllocations := numGoroutines * allocationsPerGoroutine
	fmt.Printf("优化版本: %d 次分配，耗时: %v，QPS: %.2f\n", 
		totalAllocations, duration, float64(totalAllocations)/duration.Seconds())
}

// 内存使用测试
func TestMemoryUsage(t *testing.T) {
	fmt.Println("=== 内存使用测试 ===")
	
	runtime.GC()
	var m1 runtime.MemStats
	runtime.ReadMemStats(&m1)
	
	// 测试原版本内存使用
	balancer := NewBalancer()
	for i := 0; i < 10000; i++ {
		balancer.AddNewToken(1000)
		balancer.AddNewReceiver(fmt.Sprintf("token-%d", i+1))
		balancer.AllocateProduct()
	}
	
	runtime.GC()
	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)
	
	fmt.Printf("原版本内存使用: %d KB\n", (m2.Alloc-m1.Alloc)/1024)
	
	// 测试优化版本内存使用
	runtime.GC()
	runtime.ReadMemStats(&m1)
	
	balancerOpt := NewBalancerOpt()
	for i := 0; i < 10000; i++ {
		balancerOpt.AddNewToken(1000)
		balancerOpt.AddNewReceiver(fmt.Sprintf("token-%d", i+1))
		balancerOpt.AllocateProduct()
	}
	
	runtime.GC()
	runtime.ReadMemStats(&m2)
	
	fmt.Printf("优化版本内存使用: %d KB\n", (m2.Alloc-m1.Alloc)/1024)
}
