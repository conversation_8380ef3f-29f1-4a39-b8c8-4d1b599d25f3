package main

import (
	"container/heap"
	"strconv"
	"sync"
	"sync/atomic"
)

// 优化版本的性能改进：
// 1. 使用读写锁替代互斥锁，提高并发读性能
// 2. 使用优先队列（堆）快速找到最优的 receiver 和 token
// 3. 使用原子操作减少锁竞争
// 4. 添加索引结构加速查找

var (
	receiverCounterOpt int64
	tokenCounterOpt    int64
)

type TokenOpt struct {
	ID      string
	Limit   int
	Used    int64   // 使用原子操作
	ConnNum int64   // 使用原子操作
	Score   float64 // 缓存分数，减少重复计算
}

type ReceiverOpt struct {
	ID      string
	TokenID string
	Used    int64   // 使用原子操作
	Score   float64 // 缓存分数
}

// 优先队列实现
type ReceiverHeap []*ReceiverOpt

func (h ReceiverHeap) Len() int           { return len(h) }
func (h ReceiverHeap) Less(i, j int) bool { return h[i].Score < h[j].Score }
func (h ReceiverHeap) Swap(i, j int)      { h[i], h[j] = h[j], h[i] }

func (h *ReceiverHeap) Push(x interface{}) {
	*h = append(*h, x.(*ReceiverOpt))
}

func (h *ReceiverHeap) Pop() interface{} {
	old := *h
	n := len(old)
	x := old[n-1]
	*h = old[0 : n-1]
	return x
}

type TokenHeap []*TokenOpt

func (h TokenHeap) Len() int           { return len(h) }
func (h TokenHeap) Less(i, j int) bool { return h[i].Score < h[j].Score }
func (h TokenHeap) Swap(i, j int)      { h[i], h[j] = h[j], h[i] }

func (h *TokenHeap) Push(x interface{}) {
	*h = append(*h, x.(*TokenOpt))
}

func (h *TokenHeap) Pop() interface{} {
	old := *h
	n := len(old)
	x := old[n-1]
	*h = old[0 : n-1]
	return x
}

type BalancerOpt struct {
	// 使用读写锁提高并发性能
	rwMutex sync.RWMutex

	// 基础数据结构
	Receivers map[string]*ReceiverOpt
	Tokens    map[string]*TokenOpt

	// 优先队列，用于快速找到最优选择
	receiverHeap *ReceiverHeap
	tokenHeap    *TokenHeap

	// 标记堆是否需要重建
	heapDirty bool
}

func NewBalancerOpt() *BalancerOpt {
	rh := &ReceiverHeap{}
	th := &TokenHeap{}
	heap.Init(rh)
	heap.Init(th)

	return &BalancerOpt{
		Receivers:    make(map[string]*ReceiverOpt),
		Tokens:       make(map[string]*TokenOpt),
		receiverHeap: rh,
		tokenHeap:    th,
		heapDirty:    false,
	}
}

func (b *BalancerOpt) AddNewToken(limit int) *TokenOpt {
	tokenID := generateTokenIDOpt()
	token := &TokenOpt{
		ID:    tokenID,
		Limit: limit,
		Used:  0,
		Score: 0.0,
	}

	b.rwMutex.Lock()
	b.Tokens[tokenID] = token
	heap.Push(b.tokenHeap, token)
	b.rwMutex.Unlock()

	return token
}

func (b *BalancerOpt) AddNewReceiver(tokenID string) *ReceiverOpt {
	receiverID := generateReceiverIDOpt()
	receiver := &ReceiverOpt{
		ID:      receiverID,
		TokenID: tokenID,
		Used:    0,
		Score:   0.0,
	}

	b.rwMutex.Lock()
	b.Receivers[receiverID] = receiver
	heap.Push(b.receiverHeap, receiver)
	b.rwMutex.Unlock()

	return receiver
}

// 更新分数并重新排序堆
func (b *BalancerOpt) updateScores() {
	// 更新 receiver 分数
	for _, r := range b.Receivers {
		used := atomic.LoadInt64(&r.Used)
		r.Score = float64(used) / 500.0
	}

	// 更新 token 分数
	for _, t := range b.Tokens {
		used := atomic.LoadInt64(&t.Used)
		t.Score = float64(used) / float64(t.Limit)
	}

	// 重建堆
	heap.Init(b.receiverHeap)
	heap.Init(b.tokenHeap)
	b.heapDirty = false
}

func (b *BalancerOpt) AllocateProduct() (receiverID, tokenID string) {
	b.rwMutex.Lock()
	defer b.rwMutex.Unlock()

	// 如果堆需要更新，先更新分数
	if b.heapDirty {
		b.updateScores()
	}

	// 尝试使用现有接收器
	for b.receiverHeap.Len() > 0 {
		bestReceiver := (*b.receiverHeap)[0]
		token, exists := b.Tokens[bestReceiver.TokenID]
		if !exists {
			heap.Pop(b.receiverHeap)
			continue
		}

		used := atomic.LoadInt64(&bestReceiver.Used)
		tokenUsed := atomic.LoadInt64(&token.Used)

		if used+1 <= 500 && tokenUsed+1 <= int64(token.Limit) {
			// 找到合适的接收器
			atomic.AddInt64(&bestReceiver.Used, 1)
			atomic.AddInt64(&token.Used, 1)
			b.heapDirty = true
			return bestReceiver.ID, bestReceiver.TokenID
		}

		// 当前接收器不可用，移除并继续
		heap.Pop(b.receiverHeap)
	}

	// 找不到合适的现有接收器，寻找可用的 token
	var bestToken *TokenOpt
	for b.tokenHeap.Len() > 0 {
		candidate := (*b.tokenHeap)[0]
		used := atomic.LoadInt64(&candidate.Used)
		connNum := atomic.LoadInt64(&candidate.ConnNum)

		if used+1 <= int64(candidate.Limit) && connNum < 10 {
			bestToken = candidate
			break
		}
		heap.Pop(b.tokenHeap)
	}

	if bestToken == nil {
		// 创建新令牌
		bestToken = b.AddNewToken(500)
	}

	// 创建新接收器
	newReceiver := b.AddNewReceiver(bestToken.ID)
	atomic.StoreInt64(&newReceiver.Used, 1)
	atomic.AddInt64(&bestToken.Used, 1)
	atomic.AddInt64(&bestToken.ConnNum, 1)

	b.heapDirty = true
	return newReceiver.ID, bestToken.ID
}

func generateReceiverIDOpt() string {
	counter := atomic.AddInt64(&receiverCounterOpt, 1)
	return "receiver-" + strconv.FormatInt(counter, 10)
}

func generateTokenIDOpt() string {
	counter := atomic.AddInt64(&tokenCounterOpt, 1)
	return "token-" + strconv.FormatInt(counter, 10)
}
