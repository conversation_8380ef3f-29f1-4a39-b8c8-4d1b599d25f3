package main

import (
	"fmt"
	"sync"
	"time"
)

func runPerfTest() {
	fmt.Println("=== 简化性能测试 ===")

	// 小规模测试
	fmt.Println("\n小规模测试 (10个Token, 50个Receiver, 10个并发, 每个100次分配):")
	testSmallScale()

	// 中等规模测试
	fmt.Println("\n中等规模测试 (100个Token, 500个Receiver, 50个并发, 每个200次分配):")
	testMediumScale()
}

func testSmallScale() {
	// 原版本
	fmt.Println("原版本:")
	balancer1 := NewBalancer()
	for i := 0; i < 10; i++ {
		balancer1.AddNewToken(1000)
	}
	for i := 0; i < 50; i++ {
		tokenID := fmt.Sprintf("token-%d", (i%10)+1)
		balancer1.AddNewReceiver(tokenID)
	}

	start := time.Now()
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 100; j++ {
				balancer1.AllocateProduct()
			}
		}()
	}
	wg.Wait()
	duration1 := time.Since(start)

	fmt.Printf("  耗时: %v, QPS: %.2f\n", duration1, float64(1000)/duration1.Seconds())

	// 优化版本
	fmt.Println("优化版本:")
	balancer2 := NewBalancerOpt()
	for i := 0; i < 10; i++ {
		balancer2.AddNewToken(1000)
	}
	for i := 0; i < 50; i++ {
		tokenID := fmt.Sprintf("token-%d", (i%10)+1)
		balancer2.AddNewReceiver(tokenID)
	}

	start = time.Now()
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 100; j++ {
				balancer2.AllocateProduct()
			}
		}()
	}
	wg.Wait()
	duration2 := time.Since(start)

	fmt.Printf("  耗时: %v, QPS: %.2f\n", duration2, float64(1000)/duration2.Seconds())
	fmt.Printf("  性能提升: %.2fx\n", float64(duration1)/float64(duration2))
}

func testMediumScale() {
	// 原版本
	fmt.Println("原版本:")
	balancer1 := NewBalancer()
	for i := 0; i < 100; i++ {
		balancer1.AddNewToken(1000)
	}
	for i := 0; i < 500; i++ {
		tokenID := fmt.Sprintf("token-%d", (i%100)+1)
		balancer1.AddNewReceiver(tokenID)
	}

	start := time.Now()
	var wg sync.WaitGroup
	for i := 0; i < 50; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 200; j++ {
				balancer1.AllocateProduct()
			}
		}()
	}
	wg.Wait()
	duration1 := time.Since(start)

	fmt.Printf("  耗时: %v, QPS: %.2f\n", duration1, float64(10000)/duration1.Seconds())

	// 优化版本
	fmt.Println("优化版本:")
	balancer2 := NewBalancerOpt()
	for i := 0; i < 100; i++ {
		balancer2.AddNewToken(1000)
	}
	for i := 0; i < 500; i++ {
		tokenID := fmt.Sprintf("token-%d", (i%100)+1)
		balancer2.AddNewReceiver(tokenID)
	}

	start = time.Now()
	for i := 0; i < 50; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 200; j++ {
				balancer2.AllocateProduct()
			}
		}()
	}
	wg.Wait()
	duration2 := time.Since(start)

	fmt.Printf("  耗时: %v, QPS: %.2f\n", duration2, float64(10000)/duration2.Seconds())
	fmt.Printf("  性能提升: %.2fx\n", float64(duration1)/float64(duration2))
}
