package main

import (
	"fmt"
	"sync"
	"time"
)

func main() {
	fmt.Println("=== 负载均衡器性能对比测试 ===")
	
	// 测试场景：大量数据 + 高并发
	fmt.Println("\n场景：1000个Token，5000个Receiver，100个并发goroutine，每个执行1000次分配")
	
	// 测试原版本
	fmt.Println("\n--- 原版本测试 ---")
	testOriginalPerformance()
	
	// 测试优化版本  
	fmt.Println("\n--- 优化版本测试 ---")
	testOptimizedPerformance()
	
	fmt.Println("\n=== 性能问题分析 ===")
	analyzePerformanceIssues()
}

func testOriginalPerformance() {
	balancer := NewBalancer()
	
	// 初始化大量数据
	fmt.Println("初始化数据...")
	start := time.Now()
	
	for i := 0; i < 1000; i++ {
		balancer.AddNewToken(1000)
	}
	for i := 0; i < 5000; i++ {
		tokenID := fmt.Sprintf("token-%d", (i%1000)+1)
		balancer.AddNewReceiver(tokenID)
	}
	
	initTime := time.Since(start)
	fmt.Printf("数据初始化耗时: %v\n", initTime)
	
	// 并发分配测试
	const numGoroutines = 100
	const allocationsPerGoroutine = 1000
	
	var wg sync.WaitGroup
	start = time.Now()
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < allocationsPerGoroutine; j++ {
				balancer.AllocateProduct()
			}
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	totalAllocations := numGoroutines * allocationsPerGoroutine
	qps := float64(totalAllocations) / duration.Seconds()
	
	fmt.Printf("总分配次数: %d\n", totalAllocations)
	fmt.Printf("总耗时: %v\n", duration)
	fmt.Printf("QPS: %.2f\n", qps)
	fmt.Printf("平均每次分配耗时: %v\n", duration/time.Duration(totalAllocations))
}

func testOptimizedPerformance() {
	balancer := NewBalancerOpt()
	
	// 初始化大量数据
	fmt.Println("初始化数据...")
	start := time.Now()
	
	for i := 0; i < 1000; i++ {
		balancer.AddNewToken(1000)
	}
	for i := 0; i < 5000; i++ {
		tokenID := fmt.Sprintf("token-%d", (i%1000)+1)
		balancer.AddNewReceiver(tokenID)
	}
	
	initTime := time.Since(start)
	fmt.Printf("数据初始化耗时: %v\n", initTime)
	
	// 并发分配测试
	const numGoroutines = 100
	const allocationsPerGoroutine = 1000
	
	var wg sync.WaitGroup
	start = time.Now()
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < allocationsPerGoroutine; j++ {
				balancer.AllocateProduct()
			}
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	totalAllocations := numGoroutines * allocationsPerGoroutine
	qps := float64(totalAllocations) / duration.Seconds()
	
	fmt.Printf("总分配次数: %d\n", totalAllocations)
	fmt.Printf("总耗时: %v\n", duration)
	fmt.Printf("QPS: %.2f\n", qps)
	fmt.Printf("平均每次分配耗时: %v\n", duration/time.Duration(totalAllocations))
}

func analyzePerformanceIssues() {
	fmt.Println(`
原版本的主要性能问题：

1. 【锁竞争】- 最严重的问题
   - 使用全局互斥锁，所有操作完全串行化
   - 在高并发场景下成为严重瓶颈
   - 影响：QPS 受限于单线程处理能力

2. 【算法复杂度】- O(n) 线性搜索
   - 每次分配需要遍历所有 receivers 和 tokens
   - 数据量大时，每次操作都很慢
   - 影响：随着数据量增长，性能线性下降

3. 【内存分配】- GC 压力
   - 频繁创建对象导致 GC 压力
   - map 操作可能导致内存碎片
   - 影响：长期运行时性能下降

4. 【缓存局部性】- 数据访问模式差
   - map 的随机访问模式对 CPU 缓存不友好
   - 影响：CPU 利用率低

优化版本的改进：

1. 【读写锁】- 提高并发读性能
   - 允许多个读操作并发执行
   - 只在写操作时独占锁

2. 【优先队列】- O(log n) 复杂度
   - 使用堆结构快速找到最优选择
   - 大幅减少搜索时间

3. 【原子操作】- 减少锁竞争
   - 对计数器使用原子操作
   - 减少锁的持有时间

4. 【分数缓存】- 减少重复计算
   - 缓存计算结果，避免重复计算

建议的进一步优化：

1. 【分片锁】- 将数据分片，使用多个锁
2. 【无锁数据结构】- 使用 lock-free 算法
3. 【批量操作】- 支持批量分配减少锁开销
4. 【预分配池】- 对象池减少 GC 压力
5. 【异步处理】- 将部分操作异步化
`)
}
